"""
Metrics Collection System for Enhanced Policy Generator

This module provides comprehensive metrics collection infrastructure for monitoring
the performance, reliability, and SLA compliance of the policy generation pipeline.

Features:
- Stage execution time tracking with decorators
- Success/failure rate monitoring
- AI service call metrics (latency, token usage)
- Resource utilization tracking
- SLA compliance monitoring (<120s target)
- MongoDB time-series data storage
- Lightweight, non-blocking collection
- Thread-safe operations
"""

import asyncio
import time
import functools
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Callable, Union, Set
from dataclasses import dataclass, asdict
from enum import Enum
import psutil
import json

from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo.errors import PyMongoError

from backend.utils.logging_config import get_logger
from backend.database import db as global_db # Import the global db instance


# Configure logger
logger = get_logger(__name__)


class MetricType(Enum):
    """Types of metrics collected"""
    STAGE_EXECUTION = "stage_execution"
    AI_SERVICE_CALL = "ai_service_call"
    REQUEST_THROUGHPUT = "request_throughput"
    RESOURCE_UTILIZATION = "resource_utilization"
    SLA_COMPLIANCE = "sla_compliance"
    ERROR_RATE = "error_rate"


class StageType(Enum):
    """Policy generation pipeline stages"""
    DATA_COLLECTION = "data_collection"
    RISK_ASSESSMENT = "risk_assessment"
    FRAMEWORK = "framework"
    PROCEDURES = "procedures"
    TOOLS = "tools"
    ASSEMBLY = "assembly"
    OVERALL = "overall"


@dataclass
class MetricData:
    """Core metric data structure"""
    timestamp: datetime
    metric_type: str
    stage: Optional[str]
    execution_time: Optional[float]
    success: bool
    correlation_id: Optional[str]
    metadata: Dict[str, Any]


@dataclass
class StageMetrics:
    """Metrics for a specific stage execution"""
    stage: str
    start_time: float
    end_time: Optional[float] = None
    execution_time: Optional[float] = None
    success: Optional[bool] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class AIServiceMetrics:
    """Metrics for AI service calls"""
    service_name: str
    start_time: float
    end_time: Optional[float] = None
    latency: Optional[float] = None
    token_usage: Optional[Dict[str, int]] = None
    success: Optional[bool] = None
    error_type: Optional[str] = None
    response_size: Optional[int] = None
    
    def __post_init__(self):
        if self.token_usage is None:
            self.token_usage = {}


@dataclass
class ResourceMetrics:
    """System resource utilization metrics"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    active_connections: int
    queue_depth: int


class MetricsCollector:
    """
    Central metrics collection system for the Enhanced Policy Generator.
    
    Provides decorators and direct methods for collecting various types of
    performance and operational metrics with minimal overhead.
    """
    
    def __init__(self, database: Optional[AsyncIOMotorDatabase] = None):
        """
        Initialize the metrics collector.

        Args:
            database: MongoDB database instance. If None, will get from config.
        """
        self.db = database
        self._lock = threading.Lock()
        self._active_stages: Dict[str, StageMetrics] = {}
        self._active_ai_calls: Dict[str, AIServiceMetrics] = {}
        self._request_count = 0
        self._error_count = 0
        self._sla_violations = 0
        self._start_time = time.time()
        self._background_tasks: Set[asyncio.Task] = set()

        # SLA thresholds (configurable)
        self.sla_thresholds = {
            "generation_time_seconds": 120,
            "ai_service_latency_seconds": 30,
            "success_rate_percent": 95,
            "memory_usage_mb": 2048
        }

        logger.info("MetricsCollector initialized", extra={"sla_thresholds": self.sla_thresholds})

    def _create_background_task(self, coro):
        """Create and manage background tasks with proper cleanup"""
        try:
            # Check if we have an active event loop
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # No event loop running, skip metrics collection
                logger.debug("No event loop running, skipping metrics collection")
                return

            # Check if the loop is closed
            if loop.is_closed():
                logger.debug("Event loop is closed, skipping metrics collection")
                return

            task = asyncio.create_task(coro)
            self._background_tasks.add(task)
            # Add callback to remove completed tasks
            task.add_done_callback(self._background_tasks.discard)
        except Exception as e:
            logger.debug(f"Failed to create background task: {e}")

    async def cleanup_background_tasks(self):
        """Clean up any remaining background tasks"""
        if self._background_tasks:
            logger.debug(f"Cleaning up {len(self._background_tasks)} background tasks")
            # Cancel all pending tasks
            for task in self._background_tasks:
                if not task.done():
                    task.cancel()
            # Wait for all tasks to complete or be cancelled
            if self._background_tasks:
                await asyncio.gather(*self._background_tasks, return_exceptions=True)
            self._background_tasks.clear()

    async def _get_database(self) -> AsyncIOMotorDatabase:
        """Get database instance, using the global_db if not already set."""
        if self.db is None:
            self.db = global_db
        return self.db
    
    async def _store_metric(self, metric: MetricData) -> bool:
        """
        Store metric data to MongoDB with error handling.
        
        Args:
            metric: Metric data to store
            
        Returns:
            bool: True if stored successfully, False otherwise
        """
        try:
            db = await self._get_database()
            collection = db.metrics_data
            
            # Convert to document format
            doc = {
                "timestamp": metric.timestamp,
                "metric_type": metric.metric_type,
                "stage": metric.stage,
                "execution_time": metric.execution_time,
                "success": metric.success,
                "correlation_id": metric.correlation_id,
                "metadata": metric.metadata
            }
            
            # Create TTL index if it doesn't exist (30 days retention)
            await collection.create_index("timestamp", expireAfterSeconds=2592000)
            
            await collection.insert_one(doc)
            return True
            
        except PyMongoError as e:
            logger.error(f"Failed to store metric: {e}", extra={
                "metric_type": metric.metric_type,
                "stage": metric.stage,
                "error": str(e)
            })
            return False
        except Exception as e:
            logger.error(f"Unexpected error storing metric: {e}", extra={
                "metric_type": metric.metric_type,
                "error": str(e)
            })
            return False
    
    def track_stage(self, stage: Union[StageType, str]):
        """
        Decorator to track execution time and success rate of pipeline stages.
        
        Args:
            stage: Stage name or StageType enum
            
        Usage:
            @metrics_collector.track_stage(StageType.FRAMEWORK)
            async def generate_framework(self, ...):
                # Implementation
                pass
        """
        stage_name = stage.value if isinstance(stage, StageType) else stage
        
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # Get correlation ID from context if available
                correlation_id = getattr(threading.current_thread(), 'correlation_id', None)
                
                # Start tracking
                stage_metrics = StageMetrics(
                    stage=stage_name,
                    start_time=time.time()
                )
                
                with self._lock:
                    self._active_stages[f"{stage_name}_{correlation_id}"] = stage_metrics
                
                try:
                    # Execute the function
                    result = await func(*args, **kwargs)
                    
                    # Mark as successful
                    stage_metrics.end_time = time.time()
                    stage_metrics.execution_time = stage_metrics.end_time - stage_metrics.start_time
                    stage_metrics.success = True
                    
                    # Log success
                    logger.info(f"Stage {stage_name} completed successfully", extra={
                        "stage": stage_name,
                        "execution_time": stage_metrics.execution_time,
                        "correlation_id": correlation_id
                    })
                    
                    return result
                    
                except Exception as e:
                    # Mark as failed
                    stage_metrics.end_time = time.time()
                    stage_metrics.execution_time = stage_metrics.end_time - stage_metrics.start_time
                    stage_metrics.success = False
                    stage_metrics.error_message = str(e)
                    
                    # Log failure
                    logger.error(f"Stage {stage_name} failed", extra={
                        "stage": stage_name,
                        "execution_time": stage_metrics.execution_time,
                        "error": str(e),
                        "correlation_id": correlation_id
                    })
                    
                    # Re-raise the exception
                    raise
                    
                finally:
                    # Store metrics asynchronously with proper task management
                    self._create_background_task(
                        self._finalize_stage_metrics(stage_metrics, correlation_id)
                    )

                    # Clean up active tracking
                    with self._lock:
                        self._active_stages.pop(f"{stage_name}_{correlation_id}", None)
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # For synchronous functions, wrap in async context
                return asyncio.run(async_wrapper(*args, **kwargs))
            
            # Return appropriate wrapper based on function type
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
                
        return decorator
    
    async def _finalize_stage_metrics(self, stage_metrics: StageMetrics, correlation_id: Optional[str]):
        """Store completed stage metrics"""
        metric = MetricData(
            timestamp=datetime.now(timezone.utc),
            metric_type=MetricType.STAGE_EXECUTION.value,
            stage=stage_metrics.stage,
            execution_time=stage_metrics.execution_time,
            success=stage_metrics.success or False,
            correlation_id=correlation_id,
            metadata={
                "error_message": stage_metrics.error_message,
                **stage_metrics.metadata
            }
        )
        
        await self._store_metric(metric)
        
        # Check SLA compliance
        if stage_metrics.success and stage_metrics.execution_time:
            await self._check_sla_compliance(stage_metrics.stage, stage_metrics.execution_time)
    
    def track_ai_service_call(self, service_name: str):
        """
        Decorator to track AI service call metrics.
        
        Args:
            service_name: Name of the AI service being called
            
        Usage:
            @metrics_collector.track_ai_service_call("gemini_api")
            async def call_gemini(self, ...):
                # Implementation
                pass
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                correlation_id = getattr(threading.current_thread(), 'correlation_id', None)
                
                # Start tracking
                call_metrics = AIServiceMetrics(
                    service_name=service_name,
                    start_time=time.time()
                )
                
                call_key = f"{service_name}_{correlation_id}_{id(call_metrics)}"
                
                with self._lock:
                    self._active_ai_calls[call_key] = call_metrics
                
                try:
                    # Execute the function
                    result = await func(*args, **kwargs)
                    
                    # Mark as successful and extract metrics
                    call_metrics.end_time = time.time()
                    call_metrics.latency = call_metrics.end_time - call_metrics.start_time
                    call_metrics.success = True
                    
                    # Try to extract token usage if available
                    if hasattr(result, 'usage_metadata'):
                        call_metrics.token_usage = {
                            "input_tokens": getattr(result.usage_metadata, 'prompt_token_count', 0),
                            "output_tokens": getattr(result.usage_metadata, 'candidates_token_count', 0),
                            "total_tokens": getattr(result.usage_metadata, 'total_token_count', 0)
                        }
                    
                    # Estimate response size
                    if hasattr(result, 'text'):
                        call_metrics.response_size = len(result.text.encode('utf-8'))
                    
                    return result
                    
                except Exception as e:
                    # Mark as failed
                    call_metrics.end_time = time.time()
                    call_metrics.latency = call_metrics.end_time - call_metrics.start_time
                    call_metrics.success = False
                    call_metrics.error_type = type(e).__name__
                    
                    # Increment error count
                    with self._lock:
                        self._error_count += 1
                    
                    raise
                    
                finally:
                    # Store metrics asynchronously with proper task management
                    self._create_background_task(
                        self._finalize_ai_call_metrics(call_metrics, correlation_id)
                    )

                    # Clean up active tracking
                    with self._lock:
                        self._active_ai_calls.pop(call_key, None)
            
            return async_wrapper
        return decorator
    
    async def _finalize_ai_call_metrics(self, call_metrics: AIServiceMetrics, correlation_id: Optional[str]):
        """Store completed AI service call metrics"""
        metric = MetricData(
            timestamp=datetime.now(timezone.utc),
            metric_type=MetricType.AI_SERVICE_CALL.value,
            stage=call_metrics.service_name,
            execution_time=call_metrics.latency,
            success=call_metrics.success or False,
            correlation_id=correlation_id,
            metadata={
                "service_name": call_metrics.service_name,
                "token_usage": call_metrics.token_usage,
                "response_size": call_metrics.response_size,
                "error_type": call_metrics.error_type
            }
        )
        
        await self._store_metric(metric)
        
        # Check AI service SLA
        if call_metrics.latency and call_metrics.latency > self.sla_thresholds["ai_service_latency_seconds"]:
            await self._record_sla_violation("ai_service_latency", call_metrics.latency)
    
    async def record_request_start(self, correlation_id: str):
        """Record the start of a new request"""
        with self._lock:
            self._request_count += 1
        
        metric = MetricData(
            timestamp=datetime.now(timezone.utc),
            metric_type=MetricType.REQUEST_THROUGHPUT.value,
            stage=None,
            execution_time=None,
            success=True,
            correlation_id=correlation_id,
            metadata={"event": "request_start"}
        )
        
        await self._store_metric(metric)
    
    async def collect_resource_metrics(self, queue_depth: int = 0, active_connections: int = 0):
        """
        Collect current system resource utilization metrics.
        
        Args:
            queue_depth: Current queue depth for async processing
            active_connections: Number of active connections
        """
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=None)
            memory_info = psutil.virtual_memory()
            memory_percent = memory_info.percent
            memory_mb = memory_info.used / 1024 / 1024
            
            resource_metrics = ResourceMetrics(
                timestamp=datetime.now(timezone.utc),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_mb=memory_mb,
                active_connections=active_connections,
                queue_depth=queue_depth
            )
            
            metric = MetricData(
                timestamp=resource_metrics.timestamp,
                metric_type=MetricType.RESOURCE_UTILIZATION.value,
                stage=None,
                execution_time=None,
                success=True,
                correlation_id=None,
                metadata=asdict(resource_metrics)
            )
            
            await self._store_metric(metric)
            
            # Check memory SLA
            if memory_mb > self.sla_thresholds["memory_usage_mb"]:
                await self._record_sla_violation("memory_usage", memory_mb)
            
        except Exception as e:
            logger.error(f"Failed to collect resource metrics: {e}")
    
    async def _check_sla_compliance(self, stage: str, execution_time: float):
        """Check if execution time meets SLA requirements"""
        if stage == StageType.OVERALL.value and execution_time > self.sla_thresholds["generation_time_seconds"]:
            await self._record_sla_violation("generation_time", execution_time)
    
    async def _record_sla_violation(self, violation_type: str, actual_value: float):
        """Record an SLA violation"""
        with self._lock:
            self._sla_violations += 1
        
        metric = MetricData(
            timestamp=datetime.now(timezone.utc),
            metric_type=MetricType.SLA_COMPLIANCE.value,
            stage=violation_type,
            execution_time=actual_value,
            success=False,
            correlation_id=None,
            metadata={
                "violation_type": violation_type,
                "actual_value": actual_value,
                "threshold": self.sla_thresholds.get(f"{violation_type}_seconds", 
                                                   self.sla_thresholds.get(f"{violation_type}_mb", 0))
            }
        )
        
        await self._store_metric(metric)
        
        logger.warning(f"SLA violation: {violation_type}", extra={
            "violation_type": violation_type,
            "actual_value": actual_value,
            "threshold": metric.metadata["threshold"]
        })
    
    async def get_current_stats(self) -> Dict[str, Any]:
        """Get current operational statistics"""
        uptime = time.time() - self._start_time
        
        with self._lock:
            active_stages_count = len(self._active_stages)
            active_ai_calls_count = len(self._active_ai_calls)
            total_requests = self._request_count
            total_errors = self._error_count
            total_sla_violations = self._sla_violations
        
        # Calculate success rate
        success_rate = ((total_requests - total_errors) / total_requests * 100) if total_requests > 0 else 100
        
        return {
            "uptime_seconds": uptime,
            "active_stages": active_stages_count,
            "active_ai_calls": active_ai_calls_count,
            "total_requests": total_requests,
            "total_errors": total_errors,
            "success_rate_percent": round(success_rate, 2),
            "sla_violations": total_sla_violations,
            "sla_thresholds": self.sla_thresholds
        }


# Global metrics collector instance
metrics_collector = MetricsCollector()


async def initialize_metrics_collector():
    """Initialize the global metrics collector with database connection"""
    global metrics_collector
    if metrics_collector.db is None:
        metrics_collector.db = global_db
        logger.info("MetricsCollector initialized with database connection")


# Convenience functions for common operations
async def track_generation_start(correlation_id: str):
    """Track the start of a policy generation request"""
    await metrics_collector.record_request_start(correlation_id)


async def get_metrics_summary():
    """Get current metrics summary"""
    return await metrics_collector.get_current_stats()


async def collect_system_metrics(queue_depth: int = 0, active_connections: int = 0):
    """Collect system resource metrics"""
    await metrics_collector.collect_resource_metrics(queue_depth, active_connections)