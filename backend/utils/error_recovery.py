"""
Error recovery strategies for the Enhanced Policy Generator.

This module provides stage-specific error recovery strategies, fallback mechanisms,
and recovery decision logic for handling various failure scenarios in the
policy generation pipeline.
"""

import logging
import asyncio
import time
from typing import Dict, Any, Optional, List, Callable, Union, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
from abc import ABC, abstractmethod

from ..exceptions.enhanced_policy_exceptions import (
    EnhancedPolicyException,
    AIServiceException,
    AIRateLimitException,
    AIQuotaExceededException,
    FrameworkGenerationException,
    ProcedureGenerationException,
    ToolGenerationException,
    DocumentAssemblyException,
    ValidationException,
    TimeoutException,
    ConfigurationException,
    create_exception
)
from .logging_config import get_service_logger, correlation_context, performance_timer


class RecoveryStrategy(Enum):
    """Available recovery strategies."""
    RETRY = "retry"
    FALLBACK = "fallback"
    DEGRADE = "degrade"
    SKIP = "skip"
    ABORT = "abort"
    CACHE = "cache"
    MANUAL_INTERVENTION = "manual_intervention"


class RecoveryDecision(Enum):
    """Recovery decision outcomes."""
    CONTINUE = "continue"
    RETRY_STAGE = "retry_stage"
    SKIP_STAGE = "skip_stage"
    USE_FALLBACK = "use_fallback"
    DEGRADE_QUALITY = "degrade_quality"
    ABORT_GENERATION = "abort_generation"
    CACHE_RESULT = "cache_result"
    ESCALATE = "escalate"


@dataclass
class RecoveryContext:
    """Context information for error recovery decisions."""
    stage: str
    error: Exception
    attempt_count: int
    total_attempts: int
    elapsed_time: float
    correlation_id: Optional[str] = None
    request_context: Dict[str, Any] = field(default_factory=dict)
    previous_errors: List[Exception] = field(default_factory=list)
    available_fallbacks: List[str] = field(default_factory=list)
    cache_available: bool = False
    user_tolerance: str = "normal"  # low, normal, high
    time_budget_remaining: Optional[float] = None
    stage_config: Optional[Dict[str, Any]] = None
    allow_fallback: bool = True
    allow_degradation: bool = True
    allow_skip: bool = False
    quality_threshold: float = 0.6


@dataclass
class RecoveryAction:
    """Describes a recovery action to take."""
    strategy: RecoveryStrategy
    decision: RecoveryDecision
    parameters: Dict[str, Any] = field(default_factory=dict)
    fallback_content: Optional[str] = None
    degraded_quality: bool = False
    estimated_time: Optional[float] = None
    confidence: float = 1.0
    reason: str = ""


class RecoveryStrategy_Interface(ABC):
    """Base interface for recovery strategies."""
    
    @abstractmethod
    async def can_recover(self, context: RecoveryContext) -> bool:
        """Determine if this strategy can handle the given error context."""
        pass
    
    @abstractmethod
    async def recover(self, context: RecoveryContext) -> RecoveryAction:
        """Execute the recovery strategy and return the action to take."""
        pass
    
    @abstractmethod
    def get_priority(self) -> int:
        """Get the priority of this strategy (lower numbers = higher priority)."""
        pass


class RetryRecoveryStrategy(RecoveryStrategy_Interface):
    """Strategy for retry-based recovery."""
    
    def __init__(self, max_retries: int = 3, backoff_multiplier: float = 2.0):
        self.max_retries = max_retries
        self.backoff_multiplier = backoff_multiplier
        self.logger = get_service_logger()
    
    async def can_recover(self, context: RecoveryContext) -> bool:
        """Check if retry is viable."""
        # Don't retry if we've exceeded max attempts
        if context.attempt_count >= self.max_retries:
            return False
        
        # Don't retry for certain non-recoverable errors
        non_retryable_errors = [
            ConfigurationException,
            ValidationException,
            AIQuotaExceededException,
        ]
        
        if any(isinstance(context.error, err_type) for err_type in non_retryable_errors):
            return False
        
        # Don't retry if time budget is exceeded
        if context.time_budget_remaining and context.time_budget_remaining < 10:
            return False
        
        return True
    
    async def recover(self, context: RecoveryContext) -> RecoveryAction:
        """Execute retry recovery."""
        wait_time = min(
            self.backoff_multiplier ** context.attempt_count,
            30  # Max 30 seconds
        )
        
        self.logger.info(
            f"Retry recovery for {context.stage}",
            extra={
                'stage': context.stage,
                'attempt': context.attempt_count + 1,
                'max_attempts': self.max_retries,
                'wait_time': wait_time,
                'error_type': type(context.error).__name__,
                'correlation_id': context.correlation_id,
            }
        )
        
        return RecoveryAction(
            strategy=RecoveryStrategy.RETRY,
            decision=RecoveryDecision.RETRY_STAGE,
            parameters={'wait_time': wait_time},
            estimated_time=wait_time + 30,  # Wait time + estimated stage time
            confidence=max(0.9 - (context.attempt_count * 0.2), 0.3),
            reason=f"Retrying stage {context.stage} after {wait_time:.1f}s delay"
        )
    
    def get_priority(self) -> int:
        return 1  # High priority


class FallbackRecoveryStrategy(RecoveryStrategy_Interface):
    """Strategy for fallback-based recovery."""
    
    def __init__(self):
        self.logger = get_service_logger()
        self.fallback_content = {
            'framework': self._get_framework_fallback(),
            'procedures': self._get_procedures_fallback(),
            'tools': self._get_tools_fallback(),
            'assembly': self._get_assembly_fallback(),
        }
    
    async def can_recover(self, context: RecoveryContext) -> bool:
        """Check if fallback content is available."""
        stage_key = context.stage.lower().replace('_generation', '').replace('_', '')
        return stage_key in self.fallback_content or context.available_fallbacks
    
    async def recover(self, context: RecoveryContext) -> RecoveryAction:
        """Execute fallback recovery."""
        stage_key = context.stage.lower().replace('_generation', '').replace('_', '')
        fallback_content = None
        
        # Use stage-specific fallback
        if stage_key in self.fallback_content:
            fallback_content = self.fallback_content[stage_key]
        elif context.available_fallbacks:
            fallback_content = context.available_fallbacks[0]
        
        self.logger.warning(
            f"Using fallback content for {context.stage}",
            extra={
                'stage': context.stage,
                'fallback_available': fallback_content is not None,
                'error_type': type(context.error).__name__,
                'correlation_id': context.correlation_id,
            }
        )
        
        return RecoveryAction(
            strategy=RecoveryStrategy.FALLBACK,
            decision=RecoveryDecision.USE_FALLBACK,
            fallback_content=fallback_content,
            degraded_quality=True,
            estimated_time=5,  # Fast fallback
            confidence=0.6,
            reason=f"Using fallback content for {context.stage} due to {type(context.error).__name__}"
        )
    
    def get_priority(self) -> int:
        return 2  # Medium priority
    
    def _get_framework_fallback(self) -> str:
        """Get fallback framework content."""
        return """
# Compliance Framework

## Overview
This document outlines the fundamental compliance framework requirements for your organization.

## Key Compliance Areas

### Data Protection
- Implement appropriate data protection measures
- Ensure lawful processing of personal data
- Maintain data subject rights procedures

### Security Controls
- Establish information security management system
- Implement access controls and monitoring
- Conduct regular security assessments

### Governance
- Define clear roles and responsibilities
- Establish compliance monitoring procedures
- Implement incident response processes

## Implementation Approach
1. Assess current compliance status
2. Identify gaps and priorities
3. Develop implementation roadmap
4. Execute compliance measures
5. Monitor and review effectiveness

*Note: This is a basic framework template. Please customize based on your specific regulatory requirements.*
"""
    
    def _get_procedures_fallback(self) -> str:
        """Get fallback procedures content."""
        return """
# Standard Operating Procedures

## Data Processing Procedures

### Data Collection
1. Identify lawful basis for processing
2. Implement privacy notices
3. Obtain necessary consents
4. Document processing activities

### Data Storage and Security
1. Classify data based on sensitivity
2. Apply appropriate security controls
3. Implement access restrictions
4. Monitor data access and usage

### Data Subject Rights
1. Establish request handling procedures
2. Implement response timeframes
3. Verify requestor identity
4. Document all actions taken

## Incident Response Procedures

### Detection and Assessment
1. Monitor for security incidents
2. Assess incident severity and impact
3. Activate response team
4. Document incident details

### Containment and Recovery
1. Contain the incident
2. Assess and mitigate damage
3. Restore normal operations
4. Conduct post-incident review

*Note: These are template procedures. Please adapt based on your organization's specific needs.*
"""
    
    def _get_tools_fallback(self) -> str:
        """Get fallback tools content."""
        return """
# Compliance Tools and Resources

## Assessment Tools

### Compliance Checklist
- [ ] Data protection impact assessments conducted
- [ ] Privacy notices implemented and updated
- [ ] Staff training completed
- [ ] Security controls implemented
- [ ] Incident response procedures tested

### Monitoring Tools
- Regular compliance audits
- Security monitoring systems
- Data processing records
- Training completion tracking

## Documentation Templates

### Required Documents
- Privacy policy template
- Data processing agreement template
- Incident report template
- Training record template
- Risk assessment template

### Management Tools
- Compliance dashboard
- Risk register
- Action tracking system
- Document version control

## Training Resources

### Staff Training
- Data protection awareness
- Security best practices
- Incident response procedures
- Role-specific training

### Management Training
- Compliance governance
- Risk management
- Legal obligations
- Strategic planning

*Note: These are standard tools and resources. Customize based on your regulatory requirements.*
"""
    
    def _get_assembly_fallback(self) -> str:
        """Get fallback assembly template."""
        return """
# Compliance Policy Document

## Executive Summary
This document provides a comprehensive compliance policy framework designed to meet regulatory requirements and protect organizational interests.

## Framework Implementation
{framework_content}

## Operational Procedures
{procedures_content}

## Supporting Tools and Resources
{tools_content}

## Implementation Timeline
- Phase 1: Foundation (Months 1-3)
  - Establish governance structure
  - Complete initial assessments
  - Begin staff training

- Phase 2: Core Implementation (Months 4-9)
  - Implement security controls
  - Deploy monitoring systems
  - Establish operational procedures

- Phase 3: Optimization (Months 10-12)
  - Conduct comprehensive review
  - Optimize processes
  - Plan continuous improvement

## Monitoring and Review
Regular monitoring and review procedures will ensure ongoing compliance effectiveness and adaptation to changing requirements.

## Conclusion
This compliance framework provides the foundation for maintaining regulatory compliance while supporting business objectives.

*Note: This document should be customized based on specific regulatory requirements and organizational context.*
"""


class DegradationRecoveryStrategy(RecoveryStrategy_Interface):
    """Strategy for quality degradation recovery."""
    
    def __init__(self):
        self.logger = get_service_logger()
    
    async def can_recover(self, context: RecoveryContext) -> bool:
        """Check if degradation is acceptable."""
        # Allow degradation for normal or high user tolerance
        if context.user_tolerance in ['normal', 'high']:
            return True
        
        # Allow degradation if time is running out
        if context.time_budget_remaining and context.time_budget_remaining < 30:
            return True
        
        return False
    
    async def recover(self, context: RecoveryContext) -> RecoveryAction:
        """Execute degradation recovery."""
        self.logger.info(
            f"Applying quality degradation for {context.stage}",
            extra={
                'stage': context.stage,
                'user_tolerance': context.user_tolerance,
                'time_remaining': context.time_budget_remaining,
                'error_type': type(context.error).__name__,
                'correlation_id': context.correlation_id,
            }
        )
        
        return RecoveryAction(
            strategy=RecoveryStrategy.DEGRADE,
            decision=RecoveryDecision.DEGRADE_QUALITY,
            parameters={'quality_reduction': 0.3},
            degraded_quality=True,
            estimated_time=15,  # Faster with reduced quality
            confidence=0.8,
            reason=f"Reducing quality requirements for {context.stage} to ensure completion"
        )
    
    def get_priority(self) -> int:
        return 3  # Lower priority


class CacheRecoveryStrategy(RecoveryStrategy_Interface):
    """Strategy for cache-based recovery."""
    
    def __init__(self):
        self.logger = get_service_logger()
    
    async def can_recover(self, context: RecoveryContext) -> bool:
        """Check if cached content is available."""
        return context.cache_available
    
    async def recover(self, context: RecoveryContext) -> RecoveryAction:
        """Execute cache recovery."""
        self.logger.info(
            f"Using cached content for {context.stage}",
            extra={
                'stage': context.stage,
                'error_type': type(context.error).__name__,
                'correlation_id': context.correlation_id,
            }
        )
        
        return RecoveryAction(
            strategy=RecoveryStrategy.CACHE,
            decision=RecoveryDecision.CACHE_RESULT,
            estimated_time=2,  # Very fast cache retrieval
            confidence=0.7,
            reason=f"Using cached content for {context.stage}"
        )
    
    def get_priority(self) -> int:
        return 0  # Highest priority (fastest)


class SkipRecoveryStrategy(RecoveryStrategy_Interface):
    """Strategy for skipping failed stages."""
    
    def __init__(self):
        self.logger = get_service_logger()
    
    async def can_recover(self, context: RecoveryContext) -> bool:
        """Check if stage can be skipped."""
        # Only allow skipping for high user tolerance
        if context.user_tolerance == 'high':
            return True
        
        # Allow skipping if critical time constraints
        if context.time_budget_remaining and context.time_budget_remaining < 15:
            return True
        
        return False
    
    async def recover(self, context: RecoveryContext) -> RecoveryAction:
        """Execute skip recovery."""
        self.logger.warning(
            f"Skipping stage {context.stage}",
            extra={
                'stage': context.stage,
                'user_tolerance': context.user_tolerance,
                'time_remaining': context.time_budget_remaining,
                'error_type': type(context.error).__name__,
                'correlation_id': context.correlation_id,
            }
        )
        
        return RecoveryAction(
            strategy=RecoveryStrategy.SKIP,
            decision=RecoveryDecision.SKIP_STAGE,
            degraded_quality=True,
            estimated_time=0,  # No time needed
            confidence=0.4,
            reason=f"Skipping {context.stage} due to repeated failures and constraints"
        )
    
    def get_priority(self) -> int:
        return 4  # Low priority


class AbortRecoveryStrategy(RecoveryStrategy_Interface):
    """Strategy for aborting generation."""
    
    def __init__(self):
        self.logger = get_service_logger()
    
    async def can_recover(self, context: RecoveryContext) -> bool:
        """Always available as last resort."""
        return True
    
    async def recover(self, context: RecoveryContext) -> RecoveryAction:
        """Execute abort recovery."""
        self.logger.error(
            f"Aborting generation at stage {context.stage}",
            extra={
                'stage': context.stage,
                'attempt_count': context.attempt_count,
                'elapsed_time': context.elapsed_time,
                'error_type': type(context.error).__name__,
                'correlation_id': context.correlation_id,
            }
        )
        
        return RecoveryAction(
            strategy=RecoveryStrategy.ABORT,
            decision=RecoveryDecision.ABORT_GENERATION,
            estimated_time=0,
            confidence=0.0,
            reason=f"Aborting generation due to unrecoverable error in {context.stage}"
        )
    
    def get_priority(self) -> int:
        return 999  # Lowest priority (last resort)


class ErrorRecoveryManager:
    """Central manager for error recovery strategies."""
    
    def __init__(self):
        self.logger = get_service_logger()
        self.strategies: List[RecoveryStrategy_Interface] = [
            CacheRecoveryStrategy(),
            RetryRecoveryStrategy(),
            FallbackRecoveryStrategy(),
            DegradationRecoveryStrategy(),
            SkipRecoveryStrategy(),
            AbortRecoveryStrategy(),
        ]
        
        # Sort strategies by priority
        self.strategies.sort(key=lambda s: s.get_priority())
        
        # Statistics tracking
        self.recovery_stats = {
            'total_recoveries': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'strategy_usage': {},
        }
    
    async def handle_error(
        self,
        stage: str,
        error: Exception,
        attempt_count: int = 1,
        total_attempts: int = 3,
        elapsed_time: float = 0.0,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> RecoveryAction:
        """
        Handle an error and determine the best recovery strategy.
        
        Args:
            stage: The pipeline stage where the error occurred
            error: The exception that occurred
            attempt_count: Current attempt number
            total_attempts: Total attempts allowed
            elapsed_time: Time elapsed since operation start
            correlation_id: Request correlation ID
            **kwargs: Additional context parameters
        
        Returns:
            RecoveryAction describing what action to take
        """
        
        # Create recovery context
        context = RecoveryContext(
            stage=stage,
            error=error,
            attempt_count=attempt_count,
            total_attempts=total_attempts,
            elapsed_time=elapsed_time,
            correlation_id=correlation_id,
            **kwargs
        )
        
        self.logger.error(
            f"Error in stage {stage}, attempting recovery",
            extra={
                'stage': stage,
                'error_type': type(error).__name__,
                'error_message': str(error),
                'attempt_count': attempt_count,
                'total_attempts': total_attempts,
                'elapsed_time': elapsed_time,
                'correlation_id': correlation_id,
            },
            exc_info=True
        )
        
        # Try each recovery strategy in priority order
        for strategy in self.strategies:
            try:
                if await strategy.can_recover(context):
                    with performance_timer(
                        self.logger,
                        f"recovery_strategy_{strategy.__class__.__name__}",
                        correlation_id=correlation_id
                    ):
                        action = await strategy.recover(context)
                    
                    # Update statistics
                    self._update_stats(strategy.__class__.__name__, True)
                    
                    self.logger.info(
                        f"Recovery strategy selected: {action.strategy.value}",
                        extra={
                            'stage': stage,
                            'strategy': action.strategy.value,
                            'decision': action.decision.value,
                            'confidence': action.confidence,
                            'reason': action.reason,
                            'correlation_id': correlation_id,
                        }
                    )
                    
                    return action
                    
            except Exception as strategy_error:
                self.logger.warning(
                    f"Recovery strategy {strategy.__class__.__name__} failed",
                    extra={
                        'strategy': strategy.__class__.__name__,
                        'error': str(strategy_error),
                        'correlation_id': correlation_id,
                    }
                )
                continue
        
        # If no strategy worked, use abort as fallback
        self._update_stats('abort_fallback', False)
        return RecoveryAction(
            strategy=RecoveryStrategy.ABORT,
            decision=RecoveryDecision.ABORT_GENERATION,
            reason="No recovery strategy could handle the error"
        )
    
    def _update_stats(self, strategy_name: str, success: bool):
        """Update recovery statistics."""
        self.recovery_stats['total_recoveries'] += 1
        
        if success:
            self.recovery_stats['successful_recoveries'] += 1
        else:
            self.recovery_stats['failed_recoveries'] += 1
        
        if strategy_name not in self.recovery_stats['strategy_usage']:
            self.recovery_stats['strategy_usage'][strategy_name] = {
                'count': 0,
                'success_count': 0,
            }
        
        self.recovery_stats['strategy_usage'][strategy_name]['count'] += 1
        if success:
            self.recovery_stats['strategy_usage'][strategy_name]['success_count'] += 1
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery statistics."""
        stats = self.recovery_stats.copy()
        
        # Calculate success rates
        for strategy_name, strategy_stats in stats['strategy_usage'].items():
            count = strategy_stats['count']
            success_count = strategy_stats['success_count']
            strategy_stats['success_rate'] = success_count / count if count > 0 else 0.0
        
        # Overall success rate
        total = stats['total_recoveries']
        stats['overall_success_rate'] = stats['successful_recoveries'] / total if total > 0 else 0.0
        
        return stats
    
    def reset_stats(self):
        """Reset recovery statistics."""
        self.recovery_stats = {
            'total_recoveries': 0,
            'successful_recoveries': 0,
            'failed_recoveries': 0,
            'strategy_usage': {},
        }


# Stage-specific recovery configurations
STAGE_RECOVERY_CONFIG = {
    'framework_generation': {
        'max_retries': 3,
        'allow_fallback': True,
        'allow_degradation': True,
        'allow_skip': False,  # Framework is critical
        'cache_timeout': 3600,  # 1 hour
        'quality_threshold': 0.7,
    },
    'procedures_generation': {
        'max_retries': 3,
        'allow_fallback': True,
        'allow_degradation': True,
        'allow_skip': False,  # Procedures are critical
        'cache_timeout': 1800,  # 30 minutes
        'quality_threshold': 0.6,
    },
    'tools_generation': {
        'max_retries': 2,
        'allow_fallback': True,
        'allow_degradation': True,
        'allow_skip': True,  # Tools can be skipped if needed
        'cache_timeout': 1800,  # 30 minutes
        'quality_threshold': 0.5,
    },
    'document_assembly': {
        'max_retries': 2,
        'allow_fallback': True,
        'allow_degradation': False,  # Assembly shouldn't be degraded
        'allow_skip': False,  # Assembly is required
        'cache_timeout': 900,   # 15 minutes
        'quality_threshold': 0.8,
    },
}


class StageSpecificRecoveryManager(ErrorRecoveryManager):
    """Extended recovery manager with stage-specific configurations."""
    
    def __init__(self, stage_config: Optional[Dict[str, Dict[str, Any]]] = None):
        super().__init__()
        self.stage_config = stage_config or STAGE_RECOVERY_CONFIG
    
    async def handle_error(
        self,
        stage: str,
        error: Exception,
        attempt_count: int = 1,
        total_attempts: int = 3,
        elapsed_time: float = 0.0,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> RecoveryAction:
        """Handle error with stage-specific configuration."""
        
        # Get stage-specific configuration
        config = self.stage_config.get(stage, {})
        
        # Override parameters with stage-specific values
        if 'max_retries' in config:
            total_attempts = min(total_attempts, config['max_retries'])
        
        # Add stage configuration to context
        kwargs.update({
            'stage_config': config,
            'allow_fallback': config.get('allow_fallback', True),
            'allow_degradation': config.get('allow_degradation', True),
            'allow_skip': config.get('allow_skip', False),
            'quality_threshold': config.get('quality_threshold', 0.6),
        })
        
        return await super().handle_error(
            stage=stage,
            error=error,
            attempt_count=attempt_count,
            total_attempts=total_attempts,
            elapsed_time=elapsed_time,
            correlation_id=correlation_id,
            **kwargs
        )


# Global recovery manager instance
_recovery_manager = None


def get_recovery_manager() -> StageSpecificRecoveryManager:
    """Get the global recovery manager instance."""
    global _recovery_manager
    if _recovery_manager is None:
        _recovery_manager = StageSpecificRecoveryManager()
    return _recovery_manager


# Recovery utility functions
async def recover_from_error(
    stage: str,
    error: Exception,
    attempt_count: int = 1,
    correlation_id: Optional[str] = None,
    **kwargs
) -> RecoveryAction:
    """
    Convenience function for error recovery.
    
    Args:
        stage: Pipeline stage where error occurred
        error: The exception
        attempt_count: Current attempt number
        correlation_id: Request correlation ID
        **kwargs: Additional context
    
    Returns:
        RecoveryAction to take
    """
    manager = get_recovery_manager()
    return await manager.handle_error(
        stage=stage,
        error=error,
        attempt_count=attempt_count,
        correlation_id=correlation_id,
        **kwargs
    )


def create_recovery_context(
    stage: str,
    error: Exception,
    attempt_count: int = 1,
    **kwargs
) -> RecoveryContext:
    """Create a recovery context for error analysis."""
    return RecoveryContext(
        stage=stage,
        error=error,
        attempt_count=attempt_count,
        **kwargs
    )


# Recovery decorators
def with_recovery(
    stage: str,
    max_attempts: int = 3,
    correlation_id_key: str = 'correlation_id'
):
    """
    Decorator to add automatic error recovery to functions.
    
    Args:
        stage: Pipeline stage name
        max_attempts: Maximum retry attempts
        correlation_id_key: Key to extract correlation ID from kwargs
    """
    def decorator(func: Callable):
        async def wrapper(*args, **kwargs):
            correlation_id = kwargs.get(correlation_id_key)
            attempt_count = 0
            last_error = None
            
            while attempt_count < max_attempts:
                attempt_count += 1
                try:
                    return await func(*args, **kwargs)
                except Exception as error:
                    last_error = error
                    
                    # Get recovery action
                    recovery_action = await recover_from_error(
                        stage=stage,
                        error=error,
                        attempt_count=attempt_count,
                        correlation_id=correlation_id,
                        **kwargs
                    )
                    
                    # Handle recovery decision
                    if recovery_action.decision == RecoveryDecision.RETRY_STAGE:
                        if 'wait_time' in recovery_action.parameters:
                            await asyncio.sleep(recovery_action.parameters['wait_time'])
                        continue
                    elif recovery_action.decision == RecoveryDecision.USE_FALLBACK:
                        return recovery_action.fallback_content
                    elif recovery_action.decision == RecoveryDecision.ABORT_GENERATION:
                        raise error
                    else:
                        # Other decisions need to be handled by caller
                        raise error
            
            # If we exhausted all attempts, raise the last error
            raise last_error or Exception(f"Failed after {max_attempts} attempts")
        
        return wrapper
    return decorator


# Export key components
__all__ = [
    'RecoveryStrategy',
    'RecoveryDecision',
    'RecoveryContext',
    'RecoveryAction',
    'ErrorRecoveryManager',
    'StageSpecificRecoveryManager',
    'get_recovery_manager',
    'recover_from_error',
    'create_recovery_context',
    'with_recovery',
    'STAGE_RECOVERY_CONFIG',
]